<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// استقبال inventory_id مشفرًا
$encrypted_inventory_id = isset($_GET['inventory_id']) ? $_GET['inventory_id'] : null;
$inventory_id = decrypt($encrypted_inventory_id, $key);

// التحقق من صحة inventory_id
if (!$inventory_id || !is_numeric($inventory_id)) {
    die("خطأ: رقم الجرد غير صالح.");
}

// جلب بيانات الجرد
$inventory_sql = "SELECT * FROM monthly_inventory WHERE inventory_id = ?";
$stmt = $conn->prepare($inventory_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$inventory_result = $stmt->get_result();
$inventory = $inventory_result->fetch_assoc();
$stmt->close();

// التحقق من أن الجرد موجود
if (!$inventory) {
    die("خطأ: لم يتم العثور على بيانات الجرد.");
}

// Set the encrypted store ID
$encrypted_store_id = encrypt($inventory['store_id'], $key);

// جلب بيانات المخزن
$store_sql = "SELECT * FROM stores WHERE store_id = ?";
$stmt = $conn->prepare($store_sql);
$stmt->bind_param("i", $inventory['store_id']);
$stmt->execute();
$store_result = $stmt->get_result();
$store = $store_result->fetch_assoc();
$stmt->close();

// التحقق من أن المخزن موجود
if (!$store) {
    die("خطأ: لم يتم العثور على بيانات المخزن.");
}

// جلب بيانات الأصناف داخل الجرد
$items_sql = "SELECT items.name, monthly_inventory_items.total_recorded_quantity, monthly_inventory_items.closing_quantity, 
              monthly_inventory_items.sold_quantity, monthly_inventory_items.cost, monthly_inventory_items.price, 
              monthly_inventory_items.total_cost, monthly_inventory_items.total_sales, monthly_inventory_items.profit 
              FROM monthly_inventory_items 
              JOIN items ON monthly_inventory_items.item_id = items.item_id 
              WHERE monthly_inventory_items.inventory_id = ?";
$stmt = $conn->prepare($items_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$items_result = $stmt->get_result();
$items = $items_result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// حساب الإجماليات
$total_recorded_quantity = 0;
$total_closing_quantity = 0;
$total_sold_quantity = 0;
$total_cost = 0;
$total_sales = 0;
$total_profit = 0;
$total_remaining_cost = 0;

foreach ($items as $item) {
    $total_recorded_quantity += $item['total_recorded_quantity'];
    $total_closing_quantity += $item['closing_quantity'];
    $total_sold_quantity += $item['sold_quantity'];
    $total_cost += $item['total_cost'];
    $total_sales += $item['total_sales'];
    $total_profit += $item['profit'];
    $total_remaining_cost += $item['closing_quantity'] * $item['cost'];
}

// Fetch balance transfer data
$balance_transfers_sql = "SELECT 
    SUM(value) AS total_transfer_units, 
    SUM((sale_price - cost) * value) AS total_transfer_profit 
FROM inventory_balance_transfers 
WHERE inventory_id = ?";
$stmt = $conn->prepare($balance_transfers_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$balance_transfers_result = $stmt->get_result();
$balance_transfers = $balance_transfers_result->fetch_assoc();
$stmt->close();

$total_transfer_units = $balance_transfers['total_transfer_units'] ?? 0;
$total_transfer_profit = $balance_transfers['total_transfer_profit'] ?? 0;
$combined_profit = $total_profit + $total_transfer_profit;

// Fetch expense data
$expenses_sql = "SELECT 
    SUM(CASE WHEN expense_type = 'Expenses and Damages' THEN amount ELSE 0 END) AS total_expenses_and_damages,
    SUM(CASE WHEN expense_type = 'Credit on Store' THEN amount ELSE 0 END) AS total_credit_on_store,
    SUM(CASE WHEN expense_type = 'Credit to Store' THEN amount ELSE 0 END) AS total_credit_to_store
FROM inventory_expenses 
WHERE inventory_id = ?";
$stmt = $conn->prepare($expenses_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$expenses_result = $stmt->get_result();
$expenses = $expenses_result->fetch_assoc();
$stmt->close();

$total_expenses_and_damages = $expenses['total_expenses_and_damages'] ?? 0;
$total_credit_on_store = $expenses['total_credit_on_store'] ?? 0;
$total_credit_to_store = $expenses['total_credit_to_store'] ?? 0;

// Calculate adjusted profit
$adjusted_profit = $combined_profit - $total_expenses_and_damages - $total_credit_on_store + $total_credit_to_store;

// Fetch shift closure data
$shift_closures_sql = "SELECT 
    SUM(shift_amount) AS total_cash,
    SUM(CASE WHEN shift_type = 'morning' THEN shift_amount ELSE 0 END) AS total_morning_cash,
    SUM(CASE WHEN shift_type = 'night' THEN shift_amount ELSE 0 END) AS total_night_cash,
    SUM(purchases) AS total_purchases
FROM inventory_shift_closures 
WHERE inventory_id = ?";
$stmt = $conn->prepare($shift_closures_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$shift_closures_result = $stmt->get_result();
$shift_closures = $shift_closures_result->fetch_assoc();
$stmt->close();

$total_cash = $shift_closures['total_cash'] ?? 0;
$total_morning_cash = $shift_closures['total_morning_cash'] ?? 0;
$total_night_cash = $shift_closures['total_night_cash'] ?? 0;
$total_purchases = $shift_closures['total_purchases'] ?? 0;

// Fetch total purchase invoice value
$purchase_invoices_sql = "SELECT 
    SUM(total_amount) AS total_purchase_invoice_value 
FROM inventory_purchase_invoices 
WHERE inventory_id = ?";
$stmt = $conn->prepare($purchase_invoices_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$purchase_invoices_result = $stmt->get_result();
$purchase_invoices = $purchase_invoices_result->fetch_assoc();
$stmt->close();

$total_purchase_invoice_value = $purchase_invoices['total_purchase_invoice_value'] ?? 0;

// Use the store name from the open inventory
$store_name = $store['name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملخص الجرد - <?php echo htmlspecialchars($store['name']); ?></title>
    <!-- Include Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Loading spinner CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        .inventory-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.primary { border-left-color: #667eea; }
        .stat-card.success { border-left-color: #4facfe; }
        .stat-card.warning { border-left-color: #fa709a; }
        .stat-card.info { border-left-color: #a8edea; }
        .stat-card.danger { border-left-color: #ff9a9e; }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-card.primary .stat-icon { color: #667eea; }
        .stat-card.success .stat-icon { color: #4facfe; }
        .stat-card.warning .stat-icon { color: #fa709a; }
        .stat-card.info .stat-icon { color: #a8edea; }
        .stat-card.danger .stat-icon { color: #ff9a9e; }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .enhanced-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .table-header {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem;
            font-weight: bold;
        }

        .export-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .export-btn {
            background: var(--success-gradient);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            color: white;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Dark mode support */
        [data-theme="dark"] .stat-card {
            background: #161b22;
            color: #c9d1d9;
        }

        [data-theme="dark"] .enhanced-table {
            background: #161b22;
        }

        [data-theme="dark"] .stat-label {
            color: #8b949e;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }

            .inventory-header {
                padding: 1.5rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-icon {
                font-size: 2rem;
            }

            .stat-value {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
   
    <?php include 'sidebar.php'; ?>

    <main class="container my-5">
        <div class="container">
            <!-- Enhanced Header Section -->
            <div class="inventory-header animate__animated animate__fadeInDown">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-clipboard-list me-3"></i>
                            ملخص الجرد الشهري
                        </h1>
                        <div class="d-flex flex-wrap gap-3">
                            <span class="badge bg-light text-dark fs-6">
                                <i class="fas fa-store me-2"></i>
                                الفرع: <?php echo htmlspecialchars($store['name']); ?>
                            </span>
                            <span class="badge bg-light text-dark fs-6">
                                <i class="fas fa-calendar me-2"></i>
                                تاريخ الجرد: <?php echo date('d/m/Y', strtotime($inventory['inventory_date'])); ?>
                            </span>
                            <span class="badge bg-<?php echo $inventory['status'] === 'confirmed' ? 'success' : 'warning'; ?> fs-6">
                                <i class="fas fa-<?php echo $inventory['status'] === 'confirmed' ? 'check-circle' : 'clock'; ?> me-2"></i>
                                <?php echo $inventory['status'] === 'confirmed' ? 'مؤكد' : 'قيد الانتظار'; ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="export-section">
                            <a href="export_inventory_summary.php?inventory_id=<?php echo urlencode($encrypted_inventory_id); ?>"
                               class="export-btn animate__animated animate__pulse animate__infinite">
                                <i class="fas fa-file-export me-2"></i>
                                تصدير إلى Excel
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>

            <!-- Key Statistics Cards -->
            <div class="stats-grid animate__animated animate__fadeInUp">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($total_recorded_quantity); ?></div>
                    <div class="stat-label">إجمالي الكمية المسجلة</div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($total_sales, 2); ?></div>
                    <div class="stat-label">إجمالي المبيعات</div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($adjusted_profit, 2); ?></div>
                    <div class="stat-label">المكسب بعد المعادلة</div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($total_closing_quantity); ?></div>
                    <div class="stat-label">الكمية المتبقية</div>
                </div>
            </div>

            <!-- Enhanced Main Statistics Table -->
            <div class="enhanced-table animate__animated animate__fadeInUp">
                <div class="table-header">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        الإحصائيات التفصيلية للجرد
                    </h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover text-center mb-0">
                        <thead class="table-primary">
                            <tr>
                                <th><i class="fas fa-clipboard-list me-1"></i> الكمية المسجلة</th>
                                <th><i class="fas fa-boxes me-1"></i> الكمية المتبقية</th>
                                <th><i class="fas fa-shopping-cart me-1"></i> الكمية المباعة</th>
                                <th><i class="fas fa-dollar-sign me-1"></i> إجمالي التكلفة</th>
                                <th><i class="fas fa-chart-line me-1"></i> إجمالي المبيعات</th>
                                <th><i class="fas fa-coins me-1"></i> إجمالي المكسب</th>
                                <th><i class="fas fa-warehouse me-1"></i> قيمة المخزون المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-light">
                                <td class="fw-bold text-primary"><?php echo number_format($total_recorded_quantity); ?></td>
                                <td class="fw-bold text-info"><?php echo number_format($total_closing_quantity); ?></td>
                                <td class="fw-bold text-success"><?php echo number_format($total_sold_quantity); ?></td>
                                <td class="fw-bold text-warning"><?php echo number_format($total_cost, 2); ?> ج.م</td>
                                <td class="fw-bold text-success"><?php echo number_format($total_sales, 2); ?> ج.م</td>
                                <td class="fw-bold <?php echo $total_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($total_profit, 2); ?> ج.م
                                </td>
                                <td class="fw-bold text-info"><?php echo number_format($total_remaining_cost, 2); ?> ج.م</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Balance Transfers Table -->
            <div class="enhanced-table animate__animated animate__fadeInUp">
                <div class="table-header">
                    <h4 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        تحويلات الرصيد والأرباح المجمعة
                    </h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover text-center mb-0">
                        <thead class="table-success">
                            <tr>
                                <th><i class="fas fa-eye me-1"></i> التفاصيل</th>
                                <th><i class="fas fa-cubes me-1"></i> وحدات التحويل</th>
                                <th><i class="fas fa-chart-pie me-1"></i> مكسب التحويلات</th>
                                <th><i class="fas fa-calculator me-1"></i> إجمالي المكسب المجمع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-light">
                                <td>
                                    <button class="btn btn-outline-primary btn-sm rounded-pill"
                                            onclick="showBalanceTransfers(<?php echo $inventory_id; ?>)"
                                            data-bs-toggle="tooltip"
                                            title="عرض تفاصيل تحويلات الرصيد">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التفاصيل
                                    </button>
                                </td>
                                <td class="fw-bold text-primary">
                                    <i class="fas fa-cubes me-1"></i>
                                    <?php echo number_format($total_transfer_units, 2); ?>
                                </td>
                                <td class="fw-bold <?php echo $total_transfer_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <i class="fas fa-coins me-1"></i>
                                    <?php echo number_format($total_transfer_profit, 2); ?> ج.م
                                </td>
                                <td class="fw-bold <?php echo $combined_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <i class="fas fa-trophy me-1"></i>
                                    <?php echo number_format($combined_profit, 2); ?> ج.م
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Modal for Balance Transfers -->
            <div class="modal fade" id="balanceTransfersModal" tabindex="-1" aria-labelledby="balanceTransfersModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="balanceTransfersModalLabel">
                                <i class="fas fa-exchange-alt me-2"></i>
                                تفاصيل تحويلات الرصيد
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <div class="loading-spinner" id="balanceTransfersLoading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2">جاري تحميل تفاصيل التحويلات...</p>
                            </div>
                            <div class="table-responsive" id="balanceTransfersTable" style="display: none;">
                                <table class="table table-hover table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><i class="fas fa-user me-1"></i> اسم الحساب</th>
                                            <th><i class="fas fa-truck me-1"></i> المزود</th>
                                            <th><i class="fas fa-dollar-sign me-1"></i> التكلفة</th>
                                            <th><i class="fas fa-tag me-1"></i> سعر البيع</th>
                                            <th><i class="fas fa-calculator me-1"></i> القيمة</th>
                                            <th><i class="fas fa-calendar me-1"></i> تاريخ الإنشاء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="balanceTransfersTableBody">
                                        <!-- Data will be dynamically loaded here -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="balanceTransfersEmpty" style="display: none;" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد تحويلات رصيد</h5>
                                <p class="text-muted">لم يتم العثور على أي تحويلات رصيد لهذا الجرد</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i> إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function showBalanceTransfers(inventoryId) {
                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('balanceTransfersModal'));
                    modal.show();

                    // Show loading spinner
                    document.getElementById('balanceTransfersLoading').style.display = 'block';
                    document.getElementById('balanceTransfersTable').style.display = 'none';
                    document.getElementById('balanceTransfersEmpty').style.display = 'none';

                    fetch(`fetch_balance_transfers.php?inventory_id=${inventoryId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Hide loading spinner
                            document.getElementById('balanceTransfersLoading').style.display = 'none';

                            const tableBody = document.getElementById('balanceTransfersTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            if (data && data.length > 0) {
                                document.getElementById('balanceTransfersTable').style.display = 'block';

                                data.forEach((transfer, index) => {
                                    const createdAt = new Date(transfer.created_at);
                                    const options = {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: 'numeric',
                                        minute: 'numeric',
                                        hour12: true
                                    };
                                    const formattedDateTime = createdAt.toLocaleString('ar-EG', options);

                                    const row = document.createElement('tr');
                                    row.className = 'animate__animated animate__fadeInUp';
                                    row.style.animationDelay = `${index * 0.1}s`;

                                    const profit = parseFloat(transfer.sale_price) - parseFloat(transfer.cost);
                                    const profitClass = profit >= 0 ? 'text-success' : 'text-danger';

                                    row.innerHTML = `
                                        <td class="fw-bold">${transfer.account_name}</td>
                                        <td><span class="badge bg-info">${transfer.provider}</span></td>
                                        <td class="text-warning fw-bold">${parseFloat(transfer.cost).toFixed(2)} ج.م</td>
                                        <td class="text-primary fw-bold">${parseFloat(transfer.sale_price).toFixed(2)} ج.م</td>
                                        <td class="${profitClass} fw-bold">${parseFloat(transfer.value).toFixed(2)}</td>
                                        <td class="text-muted">${formattedDateTime}</td>
                                    `;
                                    tableBody.appendChild(row);
                                });
                            } else {
                                document.getElementById('balanceTransfersEmpty').style.display = 'block';
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching balance transfers:', error);
                            document.getElementById('balanceTransfersLoading').style.display = 'none';
                            document.getElementById('balanceTransfersEmpty').style.display = 'block';
                            document.getElementById('balanceTransfersEmpty').innerHTML = `
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <h5 class="text-danger">خطأ في تحميل البيانات</h5>
                                <p class="text-muted">حدث خطأ أثناء تحميل تفاصيل التحويلات. يرجى المحاولة مرة أخرى.</p>
                            `;
                        });
                }
            </script>

            <!-- Enhanced Expenses Table -->
            <div class="enhanced-table animate__animated animate__fadeInUp">
                <div class="table-header">
                    <h4 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        المصروفات والمعادلات المالية
                    </h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover text-center mb-0">
                        <thead class="table-warning">
                            <tr>
                                <th><i class="fas fa-eye me-1"></i> التفاصيل</th>
                                <th><i class="fas fa-exclamation-triangle me-1"></i> المصاريف والتوالف</th>
                                <th><i class="fas fa-arrow-down me-1"></i> أجل على المحل</th>
                                <th><i class="fas fa-arrow-up me-1"></i> أجل للمحل</th>
                                <th><i class="fas fa-calculator me-1"></i> المكسب النهائي</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-light">
                                <td>
                                    <button class="btn btn-outline-warning btn-sm rounded-pill"
                                            onclick="showExpenseDetails(<?php echo $inventory_id; ?>)"
                                            data-bs-toggle="tooltip"
                                            title="عرض تفاصيل المصروفات">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التفاصيل
                                    </button>
                                </td>
                                <td class="fw-bold text-danger">
                                    <i class="fas fa-minus-circle me-1"></i>
                                    <?php echo number_format($total_expenses_and_damages, 2); ?> ج.م
                                </td>
                                <td class="fw-bold text-warning">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    <?php echo number_format($total_credit_on_store, 2); ?> ج.م
                                </td>
                                <td class="fw-bold text-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    <?php echo number_format($total_credit_to_store, 2); ?> ج.م
                                </td>
                                <td class="fw-bold <?php echo $adjusted_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <i class="fas fa-<?php echo $adjusted_profit >= 0 ? 'trophy' : 'exclamation-triangle'; ?> me-1"></i>
                                    <?php echo number_format($adjusted_profit, 2); ?> ج.م
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Modal for Expense Details -->
            <div class="modal fade" id="expenseDetailsModal" tabindex="-1" aria-labelledby="expenseDetailsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title" id="expenseDetailsModalLabel">
                                <i class="fas fa-receipt me-2"></i>
                                تفاصيل المصروفات والمعادلات المالية
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <div class="loading-spinner" id="expenseDetailsLoading">
                                <div class="spinner-border text-warning" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2">جاري تحميل تفاصيل المصروفات...</p>
                            </div>
                            <div class="table-responsive" id="expenseDetailsTable" style="display: none;">
                                <table class="table table-hover table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><i class="fas fa-tag me-1"></i> اسم المصروف</th>
                                            <th><i class="fas fa-list me-1"></i> نوع المصروف</th>
                                            <th><i class="fas fa-dollar-sign me-1"></i> المبلغ</th>
                                            <th><i class="fas fa-calendar me-1"></i> تاريخ المصروف</th>
                                        </tr>
                                    </thead>
                                    <tbody id="expenseDetailsTableBody">
                                        <!-- Data will be dynamically loaded here -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="expenseDetailsEmpty" style="display: none;" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد مصروفات</h5>
                                <p class="text-muted">لم يتم العثور على أي مصروفات لهذا الجرد</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i> إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function showExpenseDetails(inventoryId) {
                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('expenseDetailsModal'));
                    modal.show();

                    // Show loading spinner
                    document.getElementById('expenseDetailsLoading').style.display = 'block';
                    document.getElementById('expenseDetailsTable').style.display = 'none';
                    document.getElementById('expenseDetailsEmpty').style.display = 'none';

                    fetch(`fetch_expenses.php?inventory_id=${inventoryId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Hide loading spinner
                            document.getElementById('expenseDetailsLoading').style.display = 'none';

                            const tableBody = document.getElementById('expenseDetailsTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            if (data && data.length > 0) {
                                document.getElementById('expenseDetailsTable').style.display = 'block';

                                data.forEach((expense, index) => {
                                    const expenseDate = new Date(expense.expense_date);
                                    const formattedDate = expenseDate.toLocaleDateString('ar-EG', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                    });

                                    const expenseTypeMap = {
                                        'Expenses and Damages': { text: 'مصروفات وتوالف', class: 'danger', icon: 'exclamation-triangle' },
                                        'Credit to Store': { text: 'أجل للمحل', class: 'success', icon: 'arrow-up' },
                                        'Credit on Store': { text: 'أجل على المحل', class: 'warning', icon: 'arrow-down' }
                                    };
                                    const expenseType = expenseTypeMap[expense.expense_type] || {
                                        text: expense.expense_type,
                                        class: 'secondary',
                                        icon: 'tag'
                                    };

                                    const row = document.createElement('tr');
                                    row.className = 'animate__animated animate__fadeInUp';
                                    row.style.animationDelay = `${index * 0.1}s`;

                                    row.innerHTML = `
                                        <td class="fw-bold">${expense.expense_name}</td>
                                        <td>
                                            <span class="badge bg-${expenseType.class}">
                                                <i class="fas fa-${expenseType.icon} me-1"></i>
                                                ${expenseType.text}
                                            </span>
                                        </td>
                                        <td class="fw-bold text-${expenseType.class}">
                                            ${parseFloat(expense.amount).toFixed(2)} ج.م
                                        </td>
                                        <td class="text-muted">${formattedDate}</td>
                                    `;
                                    tableBody.appendChild(row);
                                });
                            } else {
                                document.getElementById('expenseDetailsEmpty').style.display = 'block';
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching expense details:', error);
                            document.getElementById('expenseDetailsLoading').style.display = 'none';
                            document.getElementById('expenseDetailsEmpty').style.display = 'block';
                            document.getElementById('expenseDetailsEmpty').innerHTML = `
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <h5 class="text-danger">خطأ في تحميل البيانات</h5>
                                <p class="text-muted">حدث خطأ أثناء تحميل تفاصيل المصروفات. يرجى المحاولة مرة أخرى.</p>
                            `;
                        });
                }
            </script>

            <section class="mb-5">
                <div class="table-responsive">
                    <table class="table table-bordered text-center summary-table">
                        <thead class="table-light">
                            <tr>
                                <th>عرض</th>
                                <th>إجمالي النقدي</th>
                                <th>النقدي الصباحي</th>
                                <th>النقدي المسائي</th>
                                <th>إجمالي المشتريات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <button class="btn btn-info" onclick="showShiftClosures(<?php echo $inventory_id; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                                <td><?php echo number_format($total_cash, 2); ?></td>
                                <td><?php echo number_format($total_morning_cash, 2); ?></td>
                                <td><?php echo number_format($total_night_cash, 2); ?></td>
                                <td><?php echo number_format($total_purchases, 2); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Modal for Shift Closures -->
            <div id="shiftClosuresModal" class="modal">
                <div class="modal-content">
                    <button class="close" onclick="closeModal('shiftClosuresModal')">&times;</button>
                    <h3>تفاصيل الورديات</h3>
                    <div class="modal-body">
                        <table>
                            <thead>
                                <tr>
                                    <th>اسم الحساب</th>
                                    <th>تاريخ الوردية</th>
                                    <th>نوع الوردية</th>
                                    <th>المبلغ</th>
                                    <th>المشتريات</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="shiftClosuresTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <script>
                function showShiftClosures(inventoryId) {
                    fetch(`fetch_shift_closures.php?inventory_id=${inventoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('shiftClosuresTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(shift => {
                                const shiftDate = new Date(shift.shift_date);
                                const formattedDate = shiftDate.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${shift.account_name}</td>
                                    <td>${formattedDate}</td>
                                    <td>${shift.shift_type === 'morning' ? 'صباحية' : 'مسائية'}</td>
                                    <td>${parseFloat(shift.shift_amount).toFixed(2)}</td>
                                    <td>${parseFloat(shift.purchases).toFixed(2)}</td>
                                    <td>${shift.notes || 'لا توجد ملاحظات'}</td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('shiftClosuresModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching shift closures:', error));
                }
            </script>

            <section class="mb-5">
                <div class="table-responsive">
                    <table class="table table-bordered text-center summary-table">
                        <thead class="table-light">
                            <tr>
                                <th>عرض</th>
                                <th>إجمالي قيمة فواتير الشراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <button class="btn btn-info" onclick="showPurchaseInvoices(<?php echo $inventory_id; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                                <td><?php echo number_format($total_purchase_invoice_value, 2); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Modal for Purchase Invoices -->
            <div id="purchaseInvoicesModal" class="modal">
                <div class="modal-content">
                    <button class="close" onclick="closeModal('purchaseInvoicesModal')">&times;</button>
                    <h3>تفاصيل فواتير الشراء</h3>
                    <div class="modal-body">
                        <table>
                            <thead>
                                <tr>
                                    <th>اسم الحساب</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>عرض الأصناف</th>
                                </tr>
                            </thead>
                            <tbody id="purchaseInvoicesTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Modal for Invoice Items -->
            <div id="invoiceItemsModal" class="modal">
                <div class="modal-content">
                    <button class="close" onclick="closeModal('invoiceItemsModal')">&times;</button>
                    <h3>تفاصيل أصناف الفاتورة</h3>
                    <div class="modal-body">
                        <table>
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>الكمية</th>
                                    <th>جملة الصنف</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody id="invoiceItemsTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <script>
                function showPurchaseInvoices(inventoryId) {
                    fetch(`fetch_purchase_invoices.php?inventory_id=${inventoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('purchaseInvoicesTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(invoice => {
                                const createdAt = new Date(invoice.created_at);
                                const formattedDate = createdAt.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${invoice.account_name}</td>
                                    <td>${parseFloat(invoice.total_amount).toFixed(2)}</td>
                                    <td>${formattedDate}</td>
                                    <td>
                                        <button class="btn btn-info" onclick="showInvoiceItems(${invoice.inventory_invoice_id})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('purchaseInvoicesModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching purchase invoices:', error));
                }

                function showInvoiceItems(invoiceId) {
                    fetch(`fetch_inventory_invoice_items.php?invoice_id=${invoiceId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('invoiceItemsTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(item => {
                                const totalCost = (parseFloat(item.cost) * parseFloat(item.quantity)).toFixed(2);

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${item.item_name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${parseFloat(item.cost).toFixed(2)}</td>
                                    <td>${totalCost}</td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('invoiceItemsModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching invoice items:', error));
                }
            </script>

            <section>
                <div class="table-responsive">
                    <table id="itemsTable" class="table table-hover table-striped text-center summary-table">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الصنف</th>
                                <th>الكمية المسجلة</th>
                                <th>الكمية المتبقية</th>
                                <th>الكمية المباعة</th>
                                <th>التكلفة</th>
                                <th>السعر</th>
                                <th>إجمالي التكلفة</th>
                                <th>إجمالي المبيعات</th>
                                <th>المكسب</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo $item['total_recorded_quantity']; ?></td>
                                    <td><?php echo $item['closing_quantity']; ?></td>
                                    <td><?php echo $item['sold_quantity']; ?></td>
                                    <td><?php echo number_format($item['cost'], 2); ?></td>
                                    <td><?php echo number_format($item['price'], 2); ?></td>
                                    <td><?php echo number_format($item['total_cost'], 2); ?></td>
                                    <td><?php echo number_format($item['total_sales'], 2); ?></td>
                                    <td><?php echo number_format($item['profit'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="text-center">
                <?php if ($inventory['status'] === 'pending'): ?>
                    <form method="POST" action="finish_inventory.php">
                        <input type="hidden" name="inventory_id" value="<?php echo htmlspecialchars($encrypted_inventory_id); ?>">
                        <button type="submit" name="finish_inventory" class="btn btn-danger">
                            <i class="fas fa-calendar-plus"></i> بدء شهر جديد
                        </button>
                    </form>
                <?php endif; ?>
            </section>
        </div>
    </main>

    <!-- Include Bootstrap JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#itemsTable').DataTable({
                language: {
                    url: '/elwaled_market/datatables/i18n/ar.json' // Ensure the correct path
                }
            });
        });
    </script>
        <?php include 'notifications.php'; ?>
    <!-- Add CSS override to ensure global footer appears above content -->
    <style>
        .page-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 3000 !important;
        }
    </style>
</body>
</html>

<?php $conn->close(); ?>
